# 2025年挑战杯初赛B榜补充材料&QA
# 本材料为指导文档的补充材料，此外针对一些高发问题，本材料予以说明，并持续更新。
## 1. 选手开发流程指引
    1. 开发competition_model.py和算子
    2. 在modelarts上跑通自己的模型
    3. 生成requirements.txt（指导文档已给出参考代码）
    4. 下载相关依赖包（指导文档已给出参考代码）
    5. 生成对应custom_kernels文件夹并按指导文档要求放入算子文件
    6. 打包模型包（python中的zipfile或其他方式）
    7. 上传到obs(通过modelarts的moxing库)
### 1.1 competition_model.py的额外示例
    指导文档中已展示了一种起服务的示例代码，本补充材料会展示另一种直接调用的示例代码供选手们参考，详情见文档末尾。

## 2. 常见问题QA
### 2.1 上传作品“确定”按钮点击无响应
    问题描述：作品上传页面将链接复制入框内后，点击确定按钮后，页面无反应。
    问题原因：浏览器防火墙差异，需要设置信任该链接
    解决方法：在浏览器中打开该链接，会弹出安全警告页面，在下方的“高级”中点击信任后，再将链接提交，点击确定按钮即可
### 2.2 上传失败
    错误原因：压缩包格式错误导致头文件校验失败
    解决方法：
    1. 请检查自己的压缩包格式是否为zip（浏览器输入模型包链接后发起下载至本地查看）
    2. 多尝试几次如依旧未成功，请联系官方人员
### 2.3 The file competition_model.py is not exist
    错误原因：模型包结构错误，压缩后的模型包结构不符合规范，比如：点击上级文件夹直接进行压缩。
    解决方案：重新压缩
        正确结构：双击打开模型包.zip，能看到competition_model.py。
        错误结构：双击打开模型包.zip，看到的是文件夹，点击文件夹后才能看到competition_model.py。
### 2.4 Unzip Error: occurred while extracting input01.zip
    错误原因：解压模型包失败，文件损坏或格式不正确
    解决方案：检查zip文件格式
### 2.5 Pip install error: Failed to install dependencies
    错误原因：依赖安装失败
    解决方案：按指导文档操作。本地验证时可通过pip添加后缀 --no-deps --force-reinstall 进行安装验证。

### 2.6 Inference Error: inference Timeout
    错误原因：答题超时
    可能原因：
    1. 模型优化后性能下降
    2. 输出token快，输入token慢，即模型答题快，读题慢
    解决方案：
    1. 提速优化。
    2. 在Modelarts中用A榜数据集做时长测试。
    3. 使用0.5B模型先跑通提交判分流程。

### 2.7 Inference Error: the number of successful inferences is 0
    错误原因：get_results返回的内容不符合比赛要求，未通过格式校验
    解决方案：检查get_results方法，是否能正常运行，生成的文件格式是否正确。Competition中的get_results示例如下：
    
```
def get_results(self,jsondata_list):
    res = {"result": {"results": []}}
    res["result"]["results"].append({"id": question_id, "content": “Your answer”})
    return res

```
<span style="color:rgb(233,30,77)">注：B榜有多个测试集文件，一共200题。针对每个测试集文件，判题系统均会调用一次get_results函数。</span>

### 2.8 Inference Error
    错误原因：
        a.模型路径不正确
        b.未安装accelerate
        c.找不到vllm
    解决方案：
        a. 请注意模型路径为相对引用,请检查路径格式正确。（路径需要带斜杠，否则会触发联网下载模型导致报错）
        b. 先确保competiton_model.py跑通后再导出依赖列表并下载。请按本材料第一章节 选手开发流程指引 进行检验。
        c. vllm仅支持在python环境中以库形式调用，即 import vllm

### 2.9 执行指导文档中的pip download命令后，出现INFO:pip is looking at multiple versions......
    错误原因：pip版本需要对齐至指导文档中的版本
    解决方案：
        a. 确保当前的pip 版本为22.3.1，可通过`pip --version`查看
        b. 如果不是，请用以下命令强制安装

```shell
python -m pip install pip==22.3.1
```
<span style="color:rgb(233,30,77)">注：如果第一次安装报错，请再执行一次该命令，然后查看pip版本，版本为22.3.1即可。</span>


### 2.10 在参考2.9后INFO的数量减少了，但仍有遗留，报错依旧存在
    解决方案：INFO或ERROR中提到的库
        1. 如果为未使用的库，可以在requirements.txt中删除，重新执行下载命令。
        2. 如果为需要使用的库，请联系官方人员。
### 2.11 Failed to import vllm_ascend_C:No module named 'vllm_ascend.vllm_ascend_C'
    解决方案：参考A榜指导文档的安装流程，遇到该问题为正常现象，无需解决，可以推理出结果即可。
    
### 2.12 自定义算子可不可以不打包为run文件？
    答：可以不打包为run文件，选手只要确保能在判题系统中完成安装/导入算子包即可。
    
    判题系统会按以下流程执行文件：
    1. 执行算子文件夹内的.run文件,如有多个，会遍历执行。(请将.run文件全部放在custom_kernels根目录下如无算子包或无run文件不会导致判题报错）
    2. 安装requirements.txt内的三方库。（安装算子的.whl文件请放在dependencies文件夹中，并在requirements.txt中对应添加`（格式：包名==版本，如custom_op==1.0）`）
    3. 调用competition_model.py并判分。
    
    



## 3. 新增competition_model.py示例参考
请注意，代码仅为参考实现，选手需要根据实际推理流程自行实现代码，判分仅会调用get_results方法：
<span style="color:rgb(233,30,77)">以下展示2种方式，第一种为新增，第二种方式为指导文档示例。选手可按需参考。</span>

```python
#新增#
#以下为直接加载启动的方式
#如果在过程中需要安装新的三方库，请在安装完成后，重新运行指导文档中的3.模型包文件生成参考，以生成最新依赖库列表并下载离线包。
import json
from vllm import LLM, SamplingParams
from transformers import AutoModel
class Competition:
    def __init__(self):
        #加载获取参数量
        model = AutoModel.from_pretrained(
            "./Qwen2.5-3B",
            torch_dtype="auto",
            device_map="auto"
        )
        print(f"Total parameters: {model.num_parameters() / 1e9:.2f}B")
        del model

        #以VLLM加载
        self.llm = LLM(model="./Qwen2.5-3B")  #注意相对引用路径
        self.sampling_params = {
            "choice": SamplingParams(max_tokens=1024, temperature=0.8, top_p=0.95),
            "code-generate": SamplingParams(n=3, max_tokens=2048, temperature=0.8, top_p=0.95),
            "generic-generate": SamplingParams(max_tokens=128, temperature=0.8, top_p=0.95),
            "math": SamplingParams(max_tokens=512, temperature=0.8, top_p=0.95)
        }
        self.prompt_templates = {
            "choice": "Answer the following multiple choice question. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCD. Think step by step before answering.\n\n{Question}\n\nA) {A}\nB) {B}\nC) {C}\nD) {D}",
            "code-generate": "Read the following function signature and docstring, and fully implement the function described. Your response should only contain the code for this function.\n",
            "generic-generate": "You will be asked to read a passage and answer a question. Think step by step, then write a line of the form 'Answer: $ANSWER' at the end of your response.",
            "math": "Solve the following math problem step by step. The last line of your response should be of the form Answer: \$ANSWER (without quotes) where $ANSWER is the answer to the problem.\n\n{Question}\n\nRemember to put your answer on its own line after 'Answer:', and you do need to use a \\boxed command."
        }
    def load_data(self, data_file="A-data.jsonl"):
        a_datas = []
        with open(data_file, 'r', encoding="utf-8") as f:
            for line in f:
                a_data = json.loads(line)
                a_datas.append(a_data)
        self.a_datas = a_datas
    def get_results(self,jsondata_list):
        res = {"result": {"results": []}}
        for a_data in jsondata_list:
            type_ = a_data.get("type")
            id_ = a_data.get("id")
            template = self.prompt_templates[type_]
            prompt = ""
            if type_ == "choice":
                choices = a_data["choices"]
                prompt = template.format(Question=a_data["prompt"], A=choices["A"], B=choices["B"], C=choices["C"], D=choices["D"])
            elif type_ == "math":
                prompt = template.format(Question=a_data["prompt"])
            else:
                prompt = template + a_data["prompt"]
            generated_text = []
            outputs = self.llm.generate(prompt, self.sampling_params[type_])
            for output in outputs:
                for o in output.outputs:
                    generated_text.append(o.text)
            generated_text = generated_text[0] if len(generated_text) == 1 else generated_text
            res["result"]["results"].append({"id": id_, "content": generated_text})
        return res

if __name__ == "__main__":
    comp = Competition()
    comp.load_data(data_file="A.jsonl") #load_data函数加载本地数据集用于验证跑通流程，可使用A榜数据集测试。
    res = comp.get_results(comp.a_datas[:10]) #测试前10条,在ModelArts中能顺利跑出结果后即可进行提交。
    
    #判题调用方式
    # for item in [测试集1，测试集2.....测试集n]：
    #     res = comp.get_results(item)


```
    
```python
#以下为起服务的方式参考
#如果在过程中需要安装新的三方库，请在安装完成后，重新运行指导文档中的3.模型包文件生成参考，以生成最新依赖库列表并下载离线包。
from transformers import AutoModel
import subprocess
import time
import requests
import torch
import torch_npu


TIMEOUT = 300


class Competition():
    def __init__(self):
        self.model_name = "./Qwen2.5-3B" #注意相对引用路径
        self.port = 8000
        self.server_process = None
        model = AutoModel.from_pretrained(
            self.model_name,
            torch_dtype="auto",
            device_map="auto"
        )
        print(f"Total parameters: {model.num_parameters() / 1e9:.2f}B")
        del model
        torch.npu.empty_cache()
        
        self._get_serve()
 
    def _get_serve(self):
        """加载模型、进行性能分析并启动服务"""
        try:
            self.server_process = subprocess.Popen([
                "vllm",  "serve",
                f"{self.model_name}",
                "--gpu-memory-utilization", "0.8"
            ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
            self._wait_for_service_ready()

        except Exception as e:
            print(f"初始化失败: {str(e)}")

    def _wait_for_service_ready(self):
        health_url = f"http://localhost:{self.port}/health"
        start_time = time.time()

        while time.time() - start_time < TIMEOUT:
            try:
                response = requests.get(health_url, timeout=5)
                if response.status_code == 200:
                    print("服务健康检查通过")
                    return
            except Exception as e:
                print(f"{str(e)}")

            print("等待服务启动...")
            time.sleep(30)

        raise RuntimeError("服务启动超时")

    def get_results(self, jsondata_list):
        res = {"result": {"results": []}}
        api_url = f"http://localhost:{self.port}/v1/completions"

        for a_data in jsondata_list:
            id_ = a_data.get("id")
            prompt = a_data.get("prompt")
            payload = {
                "model": self.model_name,
                "prompt": prompt,
                "max_tokens": 256,
            }

            try:
                response = requests.post(api_url, json=payload, timeout=30)
                response.raise_for_status()
                res["result"]["results"].append({"id": id_, "content": response.json()["choices"][0]["text"]})
            except requests.exceptions.RequestException as e:
                raise RuntimeError(f"API请求失败: {str(e)}")
        return res

    def shutdown(self):
        self.server_process.terminate()
        self.server_process.wait()
        print("服务已停止")

    def preprocess(self):  # 非必须
        # load data
        # feature engineering
        pass

    def postprocess(self):  # 非必须
        pass
```    

<span style="color:rgb(233,30,77)">**特别说明**：选手代码中需要输出模型的参数量，关键字为`"Total parameters: {model.num_parameters() / 1e9:.2f}B"`
</span><span style="color:rgb(233,30,77)"></span>

决赛阶段会参考单算子性能数据，选手可以参考[使用PyTorch框架接口采集和解析性能数据](https://www.hiascend.com/document/detail/zh/canncommercial/81RC1/devaids/devtools/profiling/atlasprofiling_16_0033.html)采集算子性能数据分析，如进入决赛，需在决赛PPT中呈现相关数据。

