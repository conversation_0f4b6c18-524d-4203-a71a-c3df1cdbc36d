# 2025年挑战杯初赛B榜指导文档

## 1. 判题环境约束

### 1.1 环境软件依赖

- Euler2.10.7
- Cann 8.1.RC1
- python3.9.10
- pytorch2.5.1

环境创建参考大赛A榜指导文档

### 1.2 网络环境说明

B榜判题在封闭网络下进行，无法连接外部网络，因此选手需要自行上传软件依赖包。

### 1.3 时长限制说明

判题推理限时为1小时（纯大模型推理时间），安装三方库依赖和判题推理总时间不超过2小时，超时判题将失败。

### 1.4 权限说明

判题环境没有root用户权限，请注意权限管理。

### 1.5 评分标准说明
1. 精度分共计200分，计算总分时会归一化到100分
2. 格式分共计200分，计算总分时会归一化到100分
3. 性能采用tokens/s衡量，计算总分时根据性能分的最大最小值归一化到100分计分，其中最大值为所有选手性能数据的最大tokens/s，最小值为赛题组提供的性能基线，如选手未达到性能基线，则性能得分为0分。
4. 总分 = 0.4 * 精度归一化得分 + 0.4 * 性能归一化得分 + 0.2 * 格式归一化得分 

## 2. 模型包结构

### 2.1 模型包结构示例

```shell
competition_submission.zip
├ competition_model.py
├ requirements.txt
├ dependencies
└ 其他文件、文件夹(如算子文件夹custom_kernels)等
```
Tips:
<span style="color:rgb(216,27,68)">注意压缩后的文件目录结构，请勿在上级目录一键压缩。</span>

### 2.2 模型包结构说明

#### 2.2.1 模型包规格约束

1. 上传的模型包命名为competition_submission，格式为zip，即 competition_submission.zip
    
2. 上传的模型包大小不超过10GB
    
#### 2.2.2 competition_model.py的约束

1. 有且只有一个competition_model.py文件，注意代码内的路径为相对引用。
    
2. competition_model.py包含名称为Competition的类，无入参。
    
3. Competition类内必须有方法：get_results，入参为list，每个元素都为json格式，对应当前测试用例（jsonl）内的一个json，出参为json格式字符串。
    
4. B榜有多个jsonl数据集，每次都会调用get_results得到该数据集对应的答案。判题系统会记录get_results的耗时。
    
5. 可以有其他方法，判题仅调用get_results。

#### 2.2.3 requirements.txt的约束
    
1. 有且只有一个requirements.txt文件，有且只有一个dependencies文件夹，用于安装dependencies第三方依赖包。
    
2. 大赛平台会读取requirements.txt的内容，安装dependencies文件夹内对应的三方库。
		
参考： https://support.huaweicloud.com/modelarts_faq/modelarts_05_0063.html

*请注意链接中展示了4种requirements.txt的命名格式，此次大赛约束文件名为：requirements.txt

#### 2.2.4 dependencies文件夹的约束

1. requirements.txt内列举的所需环境依赖库文件（whl,gz等格式）均存放在此文件夹。
    
2. 请勿存放其他非环境依赖相关文件。

3. 算子包所需安装的.whl文件也存放于dependencies文件夹中，并在requirements.txt中对应添加`（格式：包名==版本，如custom_op==1.0）`

#### 2.2.5 算子文件夹custom_kernels
1. 算子文件夹名为custom_kernels，没有该文件夹不会影响判题流程。

2. 算子文件夹custom_kernels根目录下须含有算子所需的.run文件

3. 安装算子的.whl文件请放在dependencies文件夹中，并在requirements.txt中对应添加`（格式：包名==版本，如custom_op==1.0）`


#### 2.2.6 其他文件、文件夹
    对于其他文件、文件夹等不做约束


### 2.3 判题执行文件顺序
    选手上传模型包后，大赛判题器会按以下流程执行文件：
    1. 执行算子文件夹内的.run文件,如有多个，会遍历执行。(请将.run文件全部放在custom_kernels根目录下)
    2. 安装requirements.txt内的三方库。
    3. 调用competition_model.py并判分。

## 3. 模型包文件生成参考

a. 确保当前的pip 版本为22.3.1，可通过`pip --version`查看
b. 如果不是，请用以下命令强制安装
<span style="color:rgb(233,30,77)">
注：如果第一次安装报错，请再执行一次该命令，然后查看pip版本，版本为22.3.1即可。</span>

```shell
python -m pip install pip==22.3.1

```

1. 通过以下命令，可生成 requirements.txt：
     
导出当前环境的包列表到requirements.txt，判题环境中已预置部分软件依赖，无需重复导出。导出软件依赖列表时可以使用以下命令跳过。

```shell
pip list --format=freeze | grep -v -E '^(ascendebug|auto_tune|hccl_parser|llm_datadist|op_compile_tool|op_gen|schedule_search|dataflow|hccl|moxing_framework|npu_bridge|npu_device|op_test_frame|opc_tool|te|vllm|vllm_ascend|llm_engine|ma_cli|ma_mindstudio_insight|ma_tensorboard|modelarts|msadvisor|msprof_analyze|auto-tune|hccl-parser|llm-datadist|op-compile-tool|op-gen|schedule-search|moxing-framework|npu-bridge|npu-device|op-test-frame|opc-tool|vllm-ascend|llm-engine|ma-cli|ma-mindstudio-insight|ma-tensorboard|msprof-analyze|msobjdump|show_kernel_debug_data|show-kernel-debug-data)=='> requirements.txt


```

#
#
2. 通过以下命令，可下载环境依赖包至dependencies文件夹

根据requirements.txt下载whl包到dependencies目录下（dependencies目录需提前创建）

```shell
pip download -r requirements.txt -d ./dependencies

```

<span style="color:rgb(233,30,77)">
若在下载其他未在以上命令中列举的库时也出现此问题：
    a. 如果在脚本中未使用，请手动将其从requirements.txt中剔除。
    b. 如果在脚本中需要使用，请联系群内官方人员解决。</span>
#
#

3. competition_model.py内Competition参考如下，**请注意，代码仅为参考实现，选手需要根据实际推理流程自行实现代码，判分仅会调用get_results方法**：
    
```python
from transformers import AutoModel
import subprocess
import time
import requests
import torch
import torch_npu


TIMEOUT = 300


class Competition():
    def __init__(self):
        self.model_name = "work/Qwen2.5-3B"
        self.port = 8000
        self.server_process = None
        model = AutoModel.from_pretrained(
            self.model_name,
            torch_dtype="auto",
            device_map="auto"
        )
        print(f"Total parameters: {model.num_parameters() / 1e9:.2f}B")
        del model
        torch.npu.empty_cache()
        
        self._get_serve()
 
    def _get_serve(self):
        """加载模型、进行性能分析并启动服务"""
        try:
            self.server_process = subprocess.Popen([
                "vllm",  "serve",
                f"{self.model_name}",
                "--gpu-memory-utilization", "0.8"
            ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
            self._wait_for_service_ready()

        except Exception as e:
            print(f"初始化失败: {str(e)}")

    def _wait_for_service_ready(self):
        health_url = f"http://localhost:{self.port}/health"
        start_time = time.time()

        while time.time() - start_time < TIMEOUT:
            try:
                response = requests.get(health_url, timeout=5)
                if response.status_code == 200:
                    print("服务健康检查通过")
                    return
            except Exception as e:
                print(f"{str(e)}")

            print("等待服务启动...")
            time.sleep(30)

        raise RuntimeError("服务启动超时")

    def get_results(self, jsondata_list):
        res = {"result": {"results": []}}
        api_url = f"http://localhost:{self.port}/v1/completions"

        for a_data in jsondata_list:
            id_ = a_data.get("id")
            prompt = a_data.get("prompt")
            payload = {
                "model": self.model_name,
                "prompt": prompt,
                "max_tokens": 256,
            }

            try:
                response = requests.post(api_url, json=payload, timeout=30)
                response.raise_for_status()
                res["result"]["results"].append({"id": id_, "content": response.json()["choices"][0]["text"]})
            except requests.exceptions.RequestException as e:
                raise RuntimeError(f"API请求失败: {str(e)}")
        return res

    def shutdown(self):
        self.server_process.terminate()
        self.server_process.wait()
        print("服务已停止")

    def preprocess(self):  # 非必须
        # load data
        # feature engineering
        pass

    def postprocess(self):  # 非必须
        pass
```    

<span style="color:rgb(233,30,77)">**特别说明**：选手代码中需要输出模型的参数量，关键字为`"Total parameters: {model.num_parameters() / 1e9:.2f}B"`
</span><span style="color:rgb(233,30,77)"></span>

决赛阶段会参考单算子性能数据，选手可以参考[使用PyTorch框架接口采集和解析性能数据](https://www.hiascend.com/document/detail/zh/canncommercial/81RC1/devaids/devtools/profiling/atlasprofiling_16_0033.html)采集算子性能数据分析，如进入决赛，需在决赛PPT中呈现相关数据。

## 4. 模型包上传参考
    1. 在大赛提交作品页面点击上传作品后，会出现如下弹窗
    2. 点击“OBS服务 ”跳转至OBS控制台页面
    3. 点击桶名进入桶目录
    4. 点击“分享”，在弹窗中调整分享时长为18小时。（注：为避免高峰传输耗时过久，请尽可能选择久的时长）
    5. 点击”复制链接”，并黏贴链接至图一中的做链接地址框内。
    6. 点击“确定”，提交作品。

图一
![](https://res-static.hc-cdn.cn/cloudbu-site/intl/zh-cn/tiaozhanbeiHW/ScreenShot_20250717143254.PNG)

图二

![](https://res-static.hc-cdn.cn/cloudbu-site/intl/zh-cn/tiaozhanbeiHW/ScreenShot_20250717144156.PNG)
图三

![](https://res-static.hc-cdn.cn/cloudbu-site/intl/zh-cn/tiaozhanbeiHW/ScreenShot_20250717144247.PNG)
图四
![](https://res-static.hc-cdn.cn/cloudbu-site/intl/zh-cn/tiaozhanbeiHW/ScreenShot_20250717144424.PNG)


## 5. 常见问题
### 5.1 上传作品“确定”按钮点击无响应
    问题描述：作品上传页面将链接复制入框内后，点击确定按钮后，页面无反应。
    问题原因：浏览器防火墙差异，需要设置信任该链接
    解决方法：在浏览器中打开该链接，会弹出安全警告页面，在下方的“高级”中点击信任后，再将链接提交，点击确定按钮即可

