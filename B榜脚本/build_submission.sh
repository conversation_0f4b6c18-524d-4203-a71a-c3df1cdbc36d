#!/bin/bash

# 华为揭榜挂帅比赛B榜模型包构建脚本
# 用于构建符合B榜要求的competition_submission.zip

echo "=== 华为揭榜挂帅比赛B榜模型包构建脚本 ==="

# 设置变量
SUBMISSION_NAME="competition_submission"
BUILD_DIR="build_temp"
FINAL_ZIP="${SUBMISSION_NAME}.zip"

# 清理之前的构建
echo "清理之前的构建文件..."
rm -rf $BUILD_DIR
rm -f $FINAL_ZIP

# 创建构建目录
echo "创建构建目录..."
mkdir -p $BUILD_DIR

# 检查必要文件
echo "检查必要文件..."
required_files=("competition_model.py" "requirements.txt")
missing_files=()

for file in "${required_files[@]}"; do
    if [ ! -f "$file" ]; then
        missing_files+=("$file")
    fi
done

if [ ${#missing_files[@]} -ne 0 ]; then
    echo "错误：缺少必要文件: ${missing_files[*]}"
    exit 1
fi

# 检查必要目录
required_dirs=("dependencies")
missing_dirs=()

for dir in "${required_dirs[@]}"; do
    if [ ! -d "$dir" ]; then
        missing_dirs+=("$dir")
    fi
done

if [ ${#missing_dirs[@]} -ne 0 ]; then
    echo "错误：缺少必要目录: ${missing_dirs[*]}"
    exit 1
fi

# 复制文件到构建目录
echo "复制文件到构建目录..."

# 复制核心文件
cp competition_model.py $BUILD_DIR/
cp requirements.txt $BUILD_DIR/

# 复制dependencies目录
cp -r dependencies $BUILD_DIR/

# 复制custom_kernels目录（如果存在）
if [ -d "custom_kernels" ]; then
    cp -r custom_kernels $BUILD_DIR/
    echo "✓ 复制custom_kernels目录"
else
    echo "⚠ custom_kernels目录不存在，跳过"
fi

# 复制其他可能需要的文件
other_files=("work" "models" "configs" "LLM-Models")
for item in "${other_files[@]}"; do
    if [ -e "$item" ]; then
        cp -r "$item" $BUILD_DIR/
        echo "✓ 复制 $item"
    fi
done

# 显示构建目录结构
echo "构建目录结构："
tree $BUILD_DIR 2>/dev/null || find $BUILD_DIR -type f

# 验证文件大小
echo "检查文件大小..."
total_size=$(du -sh $BUILD_DIR | cut -f1)
echo "总大小: $total_size"

# 创建压缩包
echo "创建压缩包..."
cd $BUILD_DIR
zip -r ../$FINAL_ZIP . -x "*.DS_Store" "*.pyc" "__pycache__/*"
cd ..

# 检查压缩包大小
if [ -f "$FINAL_ZIP" ]; then
    zip_size=$(du -h $FINAL_ZIP | cut -f1)
    echo "✓ 压缩包创建成功: $FINAL_ZIP (大小: $zip_size)"
    
    # 检查是否超过10GB限制
    zip_size_bytes=$(stat -c%s $FINAL_ZIP 2>/dev/null || stat -f%z $FINAL_ZIP)
    max_size_bytes=$((10 * 1024 * 1024 * 1024))  # 10GB
    
    if [ $zip_size_bytes -gt $max_size_bytes ]; then
        echo "⚠ 警告：压缩包大小超过10GB限制"
    else
        echo "✓ 压缩包大小符合要求"
    fi
    
    # 验证压缩包内容
    echo "压缩包内容："
    unzip -l $FINAL_ZIP
    
else
    echo "✗ 压缩包创建失败"
    exit 1
fi

# 清理构建目录
echo "清理构建目录..."
rm -rf $BUILD_DIR

echo "=== 模型包构建完成 ==="
echo "提交文件: $FINAL_ZIP"
echo ""
echo "下一步："
echo "1. 运行测试: python test_model.py"
echo "2. 上传到华为云OBS"
echo "3. 在比赛平台提交OBS链接"
