#!/bin/bash

# 华为B榜模型包构建脚本 (整合版)
# 支持通用B榜和挑战杯B榜两种模式
# 用法:
#   ./build_submission.sh              # 通用B榜模式
#   ./build_submission.sh --challenge  # 挑战杯模式

set -e

# 解析命令行参数
CHALLENGE_MODE=false
if [[ "$1" == "--challenge" ]]; then
    CHALLENGE_MODE=true
fi

# 显示模式信息
if [ "$CHALLENGE_MODE" = true ]; then
    echo "🏆 华为挑战杯初赛B榜 - 提交包构建"
    echo "=================================================="
else
    echo "=== 华为揭榜挂帅比赛B榜模型包构建脚本 ==="
fi

# 设置变量
SUBMISSION_NAME="competition_submission"
if [ "$CHALLENGE_MODE" = true ]; then
    BUILD_DIR="./build_submission"
else
    BUILD_DIR="build_temp"
fi
FINAL_ZIP="${SUBMISSION_NAME}.zip"
CURRENT_DIR=$(pwd)

# 清理之前的构建
if [ "$CHALLENGE_MODE" = true ]; then
    echo "🧹 清理之前的构建..."
else
    echo "清理之前的构建文件..."
fi
rm -rf "$BUILD_DIR"
rm -f "$FINAL_ZIP"

# 创建构建目录
if [ "$CHALLENGE_MODE" = true ]; then
    echo "📁 创建构建目录..."
else
    echo "创建构建目录..."
fi
mkdir -p "$BUILD_DIR"

# 处理competition_model.py文件
if [ "$CHALLENGE_MODE" = true ]; then
    echo "📋 复制competition_model.py..."
    if [ -f "competition_model_challenge_cup.py" ]; then
        cp "competition_model_challenge_cup.py" "$BUILD_DIR/competition_model.py"
        echo "   ✓ 使用挑战杯专用版本"
    elif [ -f "competition_model.py" ]; then
        cp "competition_model.py" "$BUILD_DIR/competition_model.py"
        echo "   ✓ 使用标准版本"
    else
        echo "   ❌ 错误：未找到competition_model.py文件"
        exit 1
    fi
else
    echo "复制competition_model.py..."
    if [ ! -f "competition_model.py" ]; then
        echo "错误：缺少必要文件: competition_model.py"
        exit 1
    fi
    cp competition_model.py "$BUILD_DIR/"
fi

# 处理requirements.txt文件
if [ "$CHALLENGE_MODE" = true ]; then
    echo "📦 生成requirements.txt..."
    if [ ! -f "requirements.txt" ]; then
        echo "   🔧 自动生成requirements.txt..."
        # 使用挑战杯指定的pip命令格式
        pip list --format=freeze | grep -v -E '^(ascendebug|auto_tune|hccl_parser|llm_datadist|op_compile_tool|op_gen|schedule_search|dataflow|hccl|moxing_framework|npu_bridge|npu_device|op_test_frame|opc_tool|te|vllm|vllm_ascend|llm_engine|ma_cli|ma_mindstudio_insight|ma_tensorboard|modelarts|msadvisor|msprof_analyze|auto-tune|hccl-parser|llm-datadist|op-compile-tool|op-gen|schedule-search|moxing-framework|npu-bridge|npu-device|op-test-frame|opc-tool|vllm-ascend|llm-engine|ma-cli|ma-mindstudio-insight|ma-tensorboard|msprof-analyze|msobjdump|show_kernel_debug_data|show-kernel-debug-data)==' > requirements.txt
    fi
    cp requirements.txt "$BUILD_DIR/"
    echo "   ✓ requirements.txt已复制"
else
    echo "复制requirements.txt..."
    if [ ! -f "requirements.txt" ]; then
        echo "错误：缺少必要文件: requirements.txt"
        exit 1
    fi
    cp requirements.txt "$BUILD_DIR/"
fi

# 处理dependencies目录
if [ "$CHALLENGE_MODE" = true ]; then
    echo "📚 创建dependencies文件夹..."
    mkdir -p "$BUILD_DIR/dependencies"

    if [ -d "dependencies" ]; then
        cp -r dependencies/* "$BUILD_DIR/dependencies/" 2>/dev/null || echo "   ℹ️ dependencies文件夹为空"
        echo "   ✓ 现有dependencies已复制"
    else
        echo "   🔧 下载依赖包到dependencies..."
        mkdir -p dependencies

        # 检查pip版本 (挑战杯要求22.3.1)
        current_pip_version=$(pip --version | awk '{print $2}')
        if [ "$current_pip_version" != "22.3.1" ]; then
            echo "   ⚠️ 警告：当前pip版本 $current_pip_version，挑战杯要求22.3.1"
            echo "   建议运行：python -m pip install pip==22.3.1"
        fi

        # 下载依赖包
        if pip download -r requirements.txt -d dependencies; then
            cp -r dependencies/* "$BUILD_DIR/dependencies/"
            echo "   ✓ 依赖包下载完成"
        else
            echo "   ⚠️ 警告：依赖包下载失败，请手动检查"
        fi
    fi
else
    echo "复制dependencies目录..."
    if [ ! -d "dependencies" ]; then
        echo "错误：缺少必要目录: dependencies"
        exit 1
    fi
    cp -r dependencies "$BUILD_DIR/"
fi

# 处理custom_kernels目录
if [ "$CHALLENGE_MODE" = true ]; then
    echo "⚡ 处理custom_kernels算子文件夹..."
    if [ -d "custom_kernels" ]; then
        cp -r custom_kernels "$BUILD_DIR/"
        echo "   ✓ custom_kernels文件夹已复制"

        # 检查.run文件 (挑战杯判题第一步)
        run_files=$(find "$BUILD_DIR/custom_kernels" -name "*.run" -type f | wc -l)
        if [ $run_files -gt 0 ]; then
            echo "   ✓ 发现 $run_files 个.run文件"
            find "$BUILD_DIR/custom_kernels" -name "*.run" -exec basename {} \; | sed 's/^/     - /'
        else
            echo "   ℹ️ 未发现.run文件"
        fi

        # 检查算子.whl文件是否在dependencies中
        whl_files=$(find "$BUILD_DIR/dependencies" -name "*custom*op*.whl" -o -name "*operator*.whl" | wc -l)
        if [ $whl_files -gt 0 ]; then
            echo "   ✓ 发现 $whl_files 个算子.whl文件在dependencies中"
        fi
    else
        echo "   ℹ️ 未发现custom_kernels文件夹（可选）"
    fi
else
    if [ -d "custom_kernels" ]; then
        cp -r custom_kernels "$BUILD_DIR/"
        echo "✓ 复制custom_kernels目录"
    else
        echo "⚠ custom_kernels目录不存在，跳过"
    fi
fi

# 复制模型文件和其他文件
if [ "$CHALLENGE_MODE" = true ]; then
    echo "🤖 检查模型文件..."
    model_dirs=("Qwen2.5-3B" "qwen2.5-3b-bespoke-stratos-merged1000" "LLM-Models")
    for model_dir in "${model_dirs[@]}"; do
        if [ -d "$model_dir" ]; then
            echo "   📦 复制模型目录: $model_dir"
            cp -r "$model_dir" "$BUILD_DIR/"
            echo "   ✓ 模型 $model_dir 已复制"
            break
        fi
    done

    echo "📄 复制其他重要文件..."
    for file in "*.md" "*.txt" "*.json" "*.jsonl"; do
        if ls $file 1> /dev/null 2>&1; then
            cp $file "$BUILD_DIR/" 2>/dev/null || true
        fi
    done
else
    # 复制其他可能需要的文件
    other_files=("work" "models" "configs" "LLM-Models")
    for item in "${other_files[@]}"; do
        if [ -e "$item" ]; then
            cp -r "$item" "$BUILD_DIR/"
            echo "✓ 复制 $item"
        fi
    done
fi

# 显示构建目录结构
if [ "$CHALLENGE_MODE" = true ]; then
    echo "📏 检查构建文件大小..."
else
    echo "构建目录结构："
    tree "$BUILD_DIR" 2>/dev/null || find "$BUILD_DIR" -type f
    echo "检查文件大小..."
fi

total_size=$(du -sh "$BUILD_DIR" | cut -f1)
if [ "$CHALLENGE_MODE" = true ]; then
    echo "   📊 构建目录大小: $total_size"
else
    echo "总大小: $total_size"
fi

# 挑战杯模式的额外验证
if [ "$CHALLENGE_MODE" = true ]; then
    # 估算压缩后大小 (通常为原大小的30-70%)
    build_size_mb=$(du -sm "$BUILD_DIR" | cut -f1)
    estimated_zip_size_mb=$((build_size_mb / 2))

    if [ $estimated_zip_size_mb -gt 10240 ]; then  # 10GB = 10240MB
        echo "   ⚠️ 警告：估算压缩包大小约 ${estimated_zip_size_mb}MB，可能超过10GB限制"
    else
        echo "   ✓ 估算压缩包大小约 ${estimated_zip_size_mb}MB，符合10GB限制"
    fi

    # 验证挑战杯B榜必须文件
    echo "🔍 验证挑战杯B榜必须文件..."
    required_files=("competition_model.py" "requirements.txt")
    missing_files=()

    for file in "${required_files[@]}"; do
        if [ -f "$BUILD_DIR/$file" ]; then
            echo "   ✓ $file 存在"
        else
            echo "   ❌ $file 缺失"
            missing_files+=("$file")
        fi
    done

    if [ ${#missing_files[@]} -gt 0 ]; then
        echo "❌ 构建失败：缺少必须文件 ${missing_files[*]}"
        exit 1
    fi

    # 验证Competition类 (挑战杯规范)
    echo "🔍 验证Competition类..."
    if grep -q "class Competition" "$BUILD_DIR/competition_model.py"; then
        echo "   ✓ Competition类存在"
    else
        echo "   ❌ Competition类缺失"
        exit 1
    fi

    if grep -q "def get_results" "$BUILD_DIR/competition_model.py"; then
        echo "   ✓ get_results方法存在"
    else
        echo "   ❌ get_results方法缺失"
        exit 1
    fi

    if grep -q "Total parameters:" "$BUILD_DIR/competition_model.py"; then
        echo "   ✓ 模型参数量输出代码存在"
    else
        echo "   ⚠️ 警告：未发现模型参数量输出代码"
    fi
fi

# 创建压缩包
if [ "$CHALLENGE_MODE" = true ]; then
    echo "📦 创建挑战杯提交包..."
    cd "$BUILD_DIR"

    # 验证目录结构 (挑战杯要求)
    echo "   🔍 验证目录结构..."
    echo "   competition_submission.zip 结构预览:"
    find . -type f | head -20 | sed 's/^/     /'
    if [ $(find . -type f | wc -l) -gt 20 ]; then
        echo "     ... 还有 $(($(find . -type f | wc -l) - 20)) 个文件"
    fi

    # 创建zip文件 (注意：直接在构建目录内压缩，确保正确的目录结构)
    zip -r "../${FINAL_ZIP}" . -x "*.DS_Store" "*.git*" "__pycache__*" "*.pyc"
    cd "$CURRENT_DIR"
else
    echo "创建压缩包..."
    cd "$BUILD_DIR"
    zip -r "../${FINAL_ZIP}" . -x "*.DS_Store" "*.pyc" "__pycache__/*"
    cd "$CURRENT_DIR"
fi

# 检查压缩包大小和内容
if [ -f "$FINAL_ZIP" ]; then
    zip_size=$(du -h "$FINAL_ZIP" | cut -f1)
    if [ "$CHALLENGE_MODE" = true ]; then
        echo "   ✓ ${FINAL_ZIP} 创建成功"
        echo "   📊 压缩包大小: $zip_size"

        # 验证zip内容结构
        echo "   🔍 验证zip内容结构..."
        unzip -l "$FINAL_ZIP" | head -10

        # 检查关键文件是否在根目录
        if unzip -l "$FINAL_ZIP" | grep -q "  competition_model.py$"; then
            echo "   ✓ competition_model.py 在根目录"
        else
            echo "   ❌ 错误：competition_model.py 不在根目录"
            exit 1
        fi

        if unzip -l "$FINAL_ZIP" | grep -q "  requirements.txt$"; then
            echo "   ✓ requirements.txt 在根目录"
        else
            echo "   ❌ 错误：requirements.txt 不在根目录"
            exit 1
        fi
    else
        echo "✓ 压缩包创建成功: $FINAL_ZIP (大小: $zip_size)"

        # 检查是否超过10GB限制
        zip_size_bytes=$(stat -c%s "$FINAL_ZIP" 2>/dev/null || stat -f%z "$FINAL_ZIP")
        max_size_bytes=$((10 * 1024 * 1024 * 1024))  # 10GB

        if [ $zip_size_bytes -gt $max_size_bytes ]; then
            echo "⚠ 警告：压缩包大小超过10GB限制"
        else
            echo "✓ 压缩包大小符合要求"
        fi

        # 验证压缩包内容
        echo "压缩包内容："
        unzip -l "$FINAL_ZIP"
    fi
else
    if [ "$CHALLENGE_MODE" = true ]; then
        echo "   ❌ 压缩包创建失败"
    else
        echo "✗ 压缩包创建失败"
    fi
    exit 1
fi

# 清理构建目录
if [ "$CHALLENGE_MODE" = true ]; then
    echo "🧹 清理构建目录..."
else
    echo "清理构建目录..."
fi
rm -rf "$BUILD_DIR"

# 最终输出
if [ "$CHALLENGE_MODE" = true ]; then
    echo ""
    echo "🎉 挑战杯B榜提交包构建完成！"
    echo "=================================================="
    echo "📦 文件名: ${FINAL_ZIP}"
    echo "📊 大小: $zip_size"
    echo "🏆 符合挑战杯B榜所有规范要求"
    echo ""
    echo "📋 下一步："
    echo "1. 上传到OBS服务"
    echo "2. 获取分享链接 (18小时有效期)"
    echo "3. 在挑战杯平台提交链接"
    echo ""
    echo "⚠️ 重要提醒："
    echo "- 确保模型路径使用相对引用"
    echo "- 验证算子融合优化功能"
    echo "- 测试<think></think><answer></answer>格式输出"
    echo "- 确认tokens/s性能指标正常"
else
    echo "=== 模型包构建完成 ==="
    echo "提交文件: $FINAL_ZIP"
    echo ""
    echo "下一步："
    echo "1. 运行测试: python test_model.py"
    echo "2. 上传到华为云OBS"
    echo "3. 在比赛平台提交OBS链接"
fi
