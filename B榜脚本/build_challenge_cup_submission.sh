#!/bin/bash
# 华为挑战杯初赛B榜 - 提交包构建脚本
# 构建符合挑战杯B榜规范的competition_submission.zip

set -e

echo "🏆 华为挑战杯初赛B榜 - 提交包构建"
echo "=" * 50

# 配置变量
SUBMISSION_NAME="competition_submission"
BUILD_DIR="./build_submission"
CURRENT_DIR=$(pwd)

# 清理之前的构建
echo "🧹 清理之前的构建..."
rm -rf "$BUILD_DIR"
rm -f "${SUBMISSION_NAME}.zip"

# 创建构建目录
echo "📁 创建构建目录..."
mkdir -p "$BUILD_DIR"

# 1. 复制competition_model.py (挑战杯B榜必须)
echo "📋 复制competition_model.py..."
if [ -f "competition_model_challenge_cup.py" ]; then
    cp "competition_model_challenge_cup.py" "$BUILD_DIR/competition_model.py"
    echo "   ✓ 使用挑战杯专用版本"
elif [ -f "competition_model.py" ]; then
    cp "competition_model.py" "$BUILD_DIR/competition_model.py"
    echo "   ✓ 使用标准版本"
else
    echo "   ❌ 错误：未找到competition_model.py文件"
    exit 1
fi

# 2. 生成requirements.txt (挑战杯规范)
echo "📦 生成requirements.txt..."
if [ ! -f "requirements.txt" ]; then
    echo "   🔧 自动生成requirements.txt..."
    # 使用挑战杯指定的pip命令格式
    pip list --format=freeze | grep -v -E '^(ascendebug|auto_tune|hccl_parser|llm_datadist|op_compile_tool|op_gen|schedule_search|dataflow|hccl|moxing_framework|npu_bridge|npu_device|op_test_frame|opc_tool|te|vllm|vllm_ascend|llm_engine|ma_cli|ma_mindstudio_insight|ma_tensorboard|modelarts|msadvisor|msprof_analyze|auto-tune|hccl-parser|llm-datadist|op-compile-tool|op-gen|schedule-search|moxing-framework|npu-bridge|npu-device|op-test-frame|opc-tool|vllm-ascend|llm-engine|ma-cli|ma-mindstudio-insight|ma-tensorboard|msprof-analyze|msobjdump|show_kernel_debug_data|show-kernel-debug-data)==' > requirements.txt
fi

cp requirements.txt "$BUILD_DIR/"
echo "   ✓ requirements.txt已复制"

# 3. 创建和填充dependencies文件夹 (挑战杯规范)
echo "📚 创建dependencies文件夹..."
mkdir -p "$BUILD_DIR/dependencies"

if [ -d "dependencies" ]; then
    cp -r dependencies/* "$BUILD_DIR/dependencies/" 2>/dev/null || echo "   ℹ️ dependencies文件夹为空"
    echo "   ✓ 现有dependencies已复制"
else
    echo "   🔧 下载依赖包到dependencies..."
    mkdir -p dependencies
    
    # 检查pip版本 (挑战杯要求22.3.1)
    current_pip_version=$(pip --version | awk '{print $2}')
    if [ "$current_pip_version" != "22.3.1" ]; then
        echo "   ⚠️ 警告：当前pip版本 $current_pip_version，挑战杯要求22.3.1"
        echo "   建议运行：python -m pip install pip==22.3.1"
    fi
    
    # 下载依赖包
    if pip download -r requirements.txt -d dependencies; then
        cp -r dependencies/* "$BUILD_DIR/dependencies/"
        echo "   ✓ 依赖包下载完成"
    else
        echo "   ⚠️ 警告：依赖包下载失败，请手动检查"
    fi
fi

# 4. 复制custom_kernels文件夹 (挑战杯算子优化)
echo "⚡ 处理custom_kernels算子文件夹..."
if [ -d "custom_kernels" ]; then
    cp -r custom_kernels "$BUILD_DIR/"
    echo "   ✓ custom_kernels文件夹已复制"
    
    # 检查.run文件 (挑战杯判题第一步)
    run_files=$(find "$BUILD_DIR/custom_kernels" -name "*.run" -type f | wc -l)
    if [ $run_files -gt 0 ]; then
        echo "   ✓ 发现 $run_files 个.run文件"
        find "$BUILD_DIR/custom_kernels" -name "*.run" -exec basename {} \; | sed 's/^/     - /'
    else
        echo "   ℹ️ 未发现.run文件"
    fi
    
    # 检查算子.whl文件是否在dependencies中
    whl_files=$(find "$BUILD_DIR/dependencies" -name "*custom*op*.whl" -o -name "*operator*.whl" | wc -l)
    if [ $whl_files -gt 0 ]; then
        echo "   ✓ 发现 $whl_files 个算子.whl文件在dependencies中"
    fi
else
    echo "   ℹ️ 未发现custom_kernels文件夹（可选）"
fi

# 5. 复制模型文件 (如果存在)
echo "🤖 检查模型文件..."
model_dirs=("Qwen2.5-3B" "qwen2.5-3b-bespoke-stratos-merged1000" "LLM-Models")
for model_dir in "${model_dirs[@]}"; do
    if [ -d "$model_dir" ]; then
        echo "   📦 复制模型目录: $model_dir"
        cp -r "$model_dir" "$BUILD_DIR/"
        echo "   ✓ 模型 $model_dir 已复制"
        break
    fi
done

# 6. 复制其他重要文件
echo "📄 复制其他重要文件..."
for file in "*.md" "*.txt" "*.json" "*.jsonl"; do
    if ls $file 1> /dev/null 2>&1; then
        cp $file "$BUILD_DIR/" 2>/dev/null || true
    fi
done

# 7. 验证挑战杯B榜必须文件
echo "🔍 验证挑战杯B榜必须文件..."
required_files=("competition_model.py" "requirements.txt")
missing_files=()

for file in "${required_files[@]}"; do
    if [ -f "$BUILD_DIR/$file" ]; then
        echo "   ✓ $file 存在"
    else
        echo "   ❌ $file 缺失"
        missing_files+=("$file")
    fi
done

if [ ${#missing_files[@]} -gt 0 ]; then
    echo "❌ 构建失败：缺少必须文件 ${missing_files[*]}"
    exit 1
fi

# 8. 验证Competition类 (挑战杯规范)
echo "🔍 验证Competition类..."
if grep -q "class Competition" "$BUILD_DIR/competition_model.py"; then
    echo "   ✓ Competition类存在"
else
    echo "   ❌ Competition类缺失"
    exit 1
fi

if grep -q "def get_results" "$BUILD_DIR/competition_model.py"; then
    echo "   ✓ get_results方法存在"
else
    echo "   ❌ get_results方法缺失"
    exit 1
fi

if grep -q "Total parameters:" "$BUILD_DIR/competition_model.py"; then
    echo "   ✓ 模型参数量输出代码存在"
else
    echo "   ⚠️ 警告：未发现模型参数量输出代码"
fi

# 9. 检查文件大小 (挑战杯限制10GB)
echo "📏 检查构建文件大小..."
build_size=$(du -sh "$BUILD_DIR" | cut -f1)
echo "   📊 构建目录大小: $build_size"

# 估算压缩后大小 (通常为原大小的30-70%)
build_size_mb=$(du -sm "$BUILD_DIR" | cut -f1)
estimated_zip_size_mb=$((build_size_mb / 2))

if [ $estimated_zip_size_mb -gt 10240 ]; then  # 10GB = 10240MB
    echo "   ⚠️ 警告：估算压缩包大小约 ${estimated_zip_size_mb}MB，可能超过10GB限制"
else
    echo "   ✓ 估算压缩包大小约 ${estimated_zip_size_mb}MB，符合10GB限制"
fi

# 10. 创建压缩包 (挑战杯规范命名)
echo "📦 创建挑战杯提交包..."
cd "$BUILD_DIR"

# 验证目录结构 (挑战杯要求)
echo "   🔍 验证目录结构..."
echo "   competition_submission.zip 结构预览:"
find . -type f | head -20 | sed 's/^/     /'
if [ $(find . -type f | wc -l) -gt 20 ]; then
    echo "     ... 还有 $(($(find . -type f | wc -l) - 20)) 个文件"
fi

# 创建zip文件 (注意：直接在构建目录内压缩，确保正确的目录结构)
zip -r "../${SUBMISSION_NAME}.zip" . -x "*.DS_Store" "*.git*" "__pycache__*" "*.pyc"

cd "$CURRENT_DIR"

# 11. 最终验证
echo "✅ 构建完成验证..."
if [ -f "${SUBMISSION_NAME}.zip" ]; then
    zip_size=$(du -sh "${SUBMISSION_NAME}.zip" | cut -f1)
    echo "   ✓ ${SUBMISSION_NAME}.zip 创建成功"
    echo "   📊 压缩包大小: $zip_size"
    
    # 验证zip内容结构
    echo "   🔍 验证zip内容结构..."
    unzip -l "${SUBMISSION_NAME}.zip" | head -10
    
    # 检查关键文件是否在根目录
    if unzip -l "${SUBMISSION_NAME}.zip" | grep -q "  competition_model.py$"; then
        echo "   ✓ competition_model.py 在根目录"
    else
        echo "   ❌ 错误：competition_model.py 不在根目录"
        exit 1
    fi
    
    if unzip -l "${SUBMISSION_NAME}.zip" | grep -q "  requirements.txt$"; then
        echo "   ✓ requirements.txt 在根目录"
    else
        echo "   ❌ 错误：requirements.txt 不在根目录"
        exit 1
    fi
else
    echo "   ❌ 压缩包创建失败"
    exit 1
fi

# 清理构建目录
echo "🧹 清理构建目录..."
rm -rf "$BUILD_DIR"

echo ""
echo "🎉 挑战杯B榜提交包构建完成！"
echo "=" * 50
echo "📦 文件名: ${SUBMISSION_NAME}.zip"
echo "📊 大小: $zip_size"
echo "🏆 符合挑战杯B榜所有规范要求"
echo ""
echo "📋 下一步："
echo "1. 上传到OBS服务"
echo "2. 获取分享链接 (18小时有效期)"
echo "3. 在挑战杯平台提交链接"
echo ""
echo "⚠️ 重要提醒："
echo "- 确保模型路径使用相对引用"
echo "- 验证算子融合优化功能"
echo "- 测试<think></think><answer></answer>格式输出"
echo "- 确认tokens/s性能指标正常" 