# 华为揭榜挂帅比赛B榜框架

## 项目概述

本项目为华为揭榜挂帅比赛B榜提供完整的推理框架，支持大语言模型的推理能力提升和性能优化。

## 目录结构

```
B榜脚本/
├── competition_model.py      # 核心推理类
├── requirements.txt          # 依赖包列表
├── dependencies/            # 依赖包文件夹
├── custom_kernels/          # 自定义算子文件夹
├── setup_dependencies.sh    # 依赖管理脚本
├── test_model.py           # 测试脚本
├── build_submission.sh     # 模型包构建脚本
└── README.md              # 说明文档
```

## 核心功能

### 1. Competition类
- **模型加载**: 支持Qwen2.5-3B等轻量级模型
- **推理服务**: 基于vLLM框架的高性能推理
- **格式处理**: 支持`<think></think><answer></answer>`格式
- **多题型支持**: choice、code-generate、generic-generate、math
- **性能监控**: 推理耗时统计和性能分析

### 2. 评分标准支持
- **精度得分**: 支持200条测试数据的精度评测
- **格式得分**: 确保输出符合指定格式要求
- **性能得分**: tokens/s性能指标优化

## 使用指南

### 1. 环境准备

```bash
# 激活conda环境
conda deactivate
conda activate python-3.9.10

# 设置环境变量
source /home/<USER>/Ascend/ascend-toolkit/set_env.sh
source /home/<USER>/Ascend/nnal/atb/set_env.sh
```

### 2. 安装依赖

```bash
# 运行依赖管理脚本
chmod +x setup_dependencies.sh
./setup_dependencies.sh
```

### 3. 测试模型

```bash
# 运行测试脚本
python test_model.py
```

### 4. 构建提交包

```bash
# 构建competition_submission.zip (通用B榜模式)
chmod +x build_submission.sh
./build_submission.sh

# 或者使用挑战杯模式 (包含额外验证)
./build_submission.sh --challenge
```

#### 构建脚本模式说明

**通用B榜模式** (`./build_submission.sh`)：
- 适用于华为揭榜挂帅比赛B榜
- 基础文件检查和打包
- 要求所有必需文件预先存在

**挑战杯模式** (`./build_submission.sh --challenge`)：
- 专门针对华为挑战杯初赛B榜
- 包含额外的验证和自动化功能
- 可自动生成requirements.txt
- 可自动下载dependencies
- 详细的Competition类验证
- 严格的目录结构检查

## 技术特性

### 1. 推理优化
- **vLLM框架**: 高性能推理引擎
- **内存优化**: GPU内存利用率控制
- **并发处理**: 支持批量推理

### 2. 算子优化支持
- **custom_kernels**: 自定义算子文件夹
- **算子融合**: 支持add+layernorm、matmul+add等融合
- **性能分析**: 集成PyTorch性能分析工具

### 3. 格式处理
- **智能格式化**: 自动包装think/answer标签
- **多题型适配**: 针对不同题型的专门处理
- **错误处理**: 鲁棒的异常处理机制

## 配置说明

### 1. 模型配置
```python
# 在competition_model.py中修改
self.model_name = "work/Qwen2.5-3B"  # 模型路径
self.port = 8000                     # 服务端口
```

### 2. 推理参数
```python
self.sampling_params = {
    "choice": {"max_tokens": 1024, "temperature": 0.8, "top_p": 0.95},
    "math": {"max_tokens": 512, "temperature": 0.8, "top_p": 0.95},
    # ... 其他配置
}
```

## 性能优化建议

### 1. 算子优化
- 实现小算子融合（add+layernorm、matmul+add等）
- 使用CANN算子开发工具
- 参考性能分析数据进行优化

### 2. 模型优化
- 量化技术应用
- 模型结构优化
- 知识蒸馏

### 3. 推理优化
- 批处理优化
- 内存管理优化
- 并发控制

## 提交流程

### 通用B榜提交流程
1. **本地测试**: 运行`python test_model.py`确保功能正常
2. **构建模型包**: 运行`./build_submission.sh`生成zip文件
3. **上传OBS**: 将zip文件上传到华为云OBS
4. **提交链接**: 在比赛平台提交OBS分享链接

### 挑战杯B榜提交流程
1. **本地测试**: 运行`python test_model.py`确保功能正常
2. **构建模型包**: 运行`./build_submission.sh --challenge`生成zip文件
3. **验证包内容**: 脚本会自动验证Competition类和必需文件
4. **上传OBS**: 将zip文件上传到华为云OBS服务
5. **获取分享链接**: 生成18小时有效期的分享链接
6. **提交链接**: 在挑战杯平台提交OBS链接

## 注意事项

### 1. 文件大小限制
- 模型包总大小不超过10GB
- 合理管理dependencies文件夹大小

### 2. 时间限制
- 推理时间限制1小时
- 总时间（包含安装）限制2小时

### 3. 格式要求
- 必须输出模型参数量信息
- 推理结果必须包含duration字段
- 支持think/answer格式要求

## 故障排除

### 1. 服务启动失败
- 检查端口是否被占用
- 确认模型路径正确
- 查看日志输出

### 2. 依赖安装失败
- 确认pip版本为22.3.1
- 检查网络连接
- 验证requirements.txt格式

### 3. 推理超时
- 调整timeout参数
- 优化推理参数
- 检查算子性能

## 后续开发建议

1. **模型微调**: 使用强化学习或知识蒸馏提升推理能力
2. **算子开发**: 实现自定义高性能算子
3. **性能调优**: 基于profiling数据进行针对性优化
4. **端侧部署**: 考虑鸿蒙等端侧应用适配

## 联系支持

如有问题，请参考比赛官方文档或联系技术支持团队。
