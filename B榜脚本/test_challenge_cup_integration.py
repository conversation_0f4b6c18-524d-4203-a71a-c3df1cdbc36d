#!/usr/bin/env python3
"""
华为挑战杯初赛B榜 - 集成测试脚本
验证自定义算子集成、格式输出、性能监控等关键功能

测试项目:
1. 算子融合功能验证 (挑战杯性能得分关键)
2. 输出格式验证 (<think></think><answer></answer>)
3. 性能监控验证 (tokens/s计算)
4. Competition类规范验证
5. 文件结构验证
"""

import sys
import os
import time
import json
import re
from pathlib import Path
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_environment_setup():
    """测试环境设置"""
    print("🏆 华为挑战杯初赛B榜 - 集成测试")
    print("=" * 60)
    
    # 检查关键环境信息
    print("📋 环境信息检查:")
    try:
        import torch
        print(f"   ✓ PyTorch版本: {torch.__version__}")
        
        import torch_npu
        print(f"   ✓ torch_npu可用: {torch.npu.is_available()}")
        print(f"   ✓ NPU设备数: {torch.npu.device_count()}")
        
        # 检查CANN环境
        ascend_path = os.environ.get('ASCEND_INSTALL_PATH', '')
        if ascend_path:
            print(f"   ✓ ASCEND_INSTALL_PATH: {ascend_path}")
        else:
            print("   ⚠️ ASCEND_INSTALL_PATH未设置")
            
    except Exception as e:
        print(f"   ❌ 环境检查失败: {e}")
        return False
    
    return True

def test_file_structure():
    """测试挑战杯文件结构"""
    print("\n📁 挑战杯文件结构验证:")
    
    required_files = [
        "competition_model_challenge_cup.py",
        "build_challenge_cup_submission.sh", 
        "挑战杯B榜自定义算子集成指南.md"
    ]
    
    missing_files = []
    for file in required_files:
        if os.path.exists(file):
            print(f"   ✓ {file}")
        else:
            print(f"   ❌ {file} - 缺失")
            missing_files.append(file)
    
    # 检查custom_kernels文件夹
    if os.path.exists("custom_kernels"):
        print("   ✓ custom_kernels/ - 存在")
        
        # 检查.run文件
        run_files = list(Path("custom_kernels").rglob("*.run"))
        if run_files:
            print(f"   ✓ 发现 {len(run_files)} 个.run文件")
            for run_file in run_files[:3]:  # 显示前3个
                print(f"     - {run_file.name}")
        else:
            print("   ℹ️ 未发现.run文件")
    else:
        print("   ℹ️ custom_kernels/ - 不存在（可选）")
    
    return len(missing_files) == 0

def test_operator_registry():
    """测试算子注册系统"""
    print("\n⚡ 算子注册系统测试:")
    
    try:
        # 导入挑战杯专用模块
        if os.path.exists("competition_model_challenge_cup.py"):
            sys.path.insert(0, ".")
            from competition_model_challenge_cup import ChallengeGulCustomOperatorRegistry
            
            # 创建注册器
            registry = ChallengeGulCustomOperatorRegistry()
            print("   ✓ 算子注册器创建成功")
            
            # 启用算子融合
            fusion_success = registry.enable_operator_fusion()
            print(f"   ✓ 算子融合启用: {'成功' if fusion_success else '失败'}")
            
            # 检查注册的算子
            perf_report = registry.get_performance_report()
            print(f"   ✓ 已注册算子数: {perf_report['total_registered']}")
            print(f"   ✓ 融合状态: {perf_report['fusion_enabled']}")
            
            # 列出融合算子
            fused_ops = [op for op in perf_report['registered_ops'] if 'fused' in op]
            print(f"   ✓ 融合算子: {fused_ops}")
            
            return True
            
        else:
            print("   ❌ competition_model_challenge_cup.py 不存在")
            return False
            
    except Exception as e:
        print(f"   ❌ 算子注册系统测试失败: {e}")
        return False

def test_operator_fusion():
    """测试算子融合功能"""
    print("\n🔧 算子融合功能测试:")
    
    try:
        import torch
        
        # 检查NPU可用性
        if not torch.npu.is_available():
            print("   ⚠️ NPU不可用，跳过融合测试")
            return True
        
        # 导入算子注册器
        from competition_model_challenge_cup import op_registry
        
        # 创建测试数据
        device = 'npu:0'
        input1 = torch.randn(2, 128, 768, dtype=torch.float16, device=device)
        input2 = torch.randn(2, 128, 768, dtype=torch.float16, device=device)
        weight = torch.ones(768, dtype=torch.float16, device=device)
        bias = torch.zeros(768, dtype=torch.float16, device=device)
        
        print("   ✓ 测试数据创建成功")
        
        # 测试融合算子
        fusion_tests = [
            ("fused_add_layernorm", "Add+LayerNorm融合"),
            ("fused_matmul_add", "MatMul+Add融合"),
            ("fused_gelu_layernorm", "GELU+LayerNorm融合")
        ]
        
        successful_fusions = 0
        for op_name, description in fusion_tests:
            try:
                if op_name == "fused_add_layernorm":
                    result = op_registry.call_operator(op_name, input1, input2, weight, bias)
                elif op_name == "fused_matmul_add":
                    weight_2d = torch.randn(768, 768, dtype=torch.float16, device=device)
                    result = op_registry.call_operator(op_name, input1, weight_2d, bias)
                elif op_name == "fused_gelu_layernorm":
                    result = op_registry.call_operator(op_name, input1, weight, bias)
                
                print(f"   ✓ {description} - 成功")
                successful_fusions += 1
                
            except Exception as e:
                print(f"   ⚠️ {description} - 回退到标准实现: {str(e)[:50]}...")
        
        print(f"   📊 融合成功率: {successful_fusions}/{len(fusion_tests)}")
        
        # 获取性能统计
        perf_report = op_registry.get_performance_report()
        if perf_report['performance']:
            print("   📈 算子调用统计:")
            for op_name, stats in perf_report['performance'].items():
                custom_rate = stats['custom_calls'] / max(stats['total_calls'], 1) * 100
                print(f"     - {op_name}: {stats['total_calls']}次调用, {custom_rate:.1f}%使用自定义实现")
        
        return successful_fusions > 0  # 至少有一个融合算子成功
        
    except Exception as e:
        print(f"   ❌ 算子融合测试失败: {e}")
        return False

def test_competition_class():
    """测试Competition类"""
    print("\n🏆 Competition类测试:")
    
    try:
        # 导入Competition类
        from competition_model_challenge_cup import Competition
        
        print("   ✓ Competition类导入成功")
        
        # 验证类结构
        required_methods = ['__init__', 'get_results']
        for method in required_methods:
            if hasattr(Competition, method):
                print(f"   ✓ {method}方法存在")
            else:
                print(f"   ❌ {method}方法缺失")
                return False
        
        # 模拟简单测试（不实际初始化，避免加载大模型）
        print("   ✓ Competition类结构验证通过")
        print("   ℹ️ 跳过实际初始化（避免模型加载）")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Competition类测试失败: {e}")
        return False

def test_output_format():
    """测试输出格式"""
    print("\n📝 输出格式测试:")
    
    try:
        # 导入格式化函数
        from competition_model_challenge_cup import Competition
        
        # 创建临时实例进行格式测试
        class MockCompetition:
            def _format_output_with_tags(self, text, type_):
                # 简化版格式化函数
                if "<think>" in text and "</think>" in text and "<answer>" in text and "</answer>" in text:
                    return text
                return f"<think>思考过程：{text[:50]}</think><answer>Answer: 测试答案</answer>"
            
            def _validate_single_output(self, text):
                format_pattern = re.compile(r'^<think>.*?</think><answer>.*?</answer>$', re.DOTALL)
                return bool(format_pattern.match(str(text).strip()))
        
        mock_comp = MockCompetition()
        
        # 测试不同类型的格式化
        test_cases = [
            ("这是一个测试答案", "choice"),
            ("函数实现代码", "code-generate"),
            ("数学计算结果", "math"),
            ("通用回答", "generic-generate")
        ]
        
        format_success = 0
        for text, type_ in test_cases:
            formatted = mock_comp._format_output_with_tags(text, type_)
            is_valid = mock_comp._validate_single_output(formatted)
            
            if is_valid:
                print(f"   ✓ {type_}格式化 - 成功")
                format_success += 1
            else:
                print(f"   ❌ {type_}格式化 - 失败")
                print(f"     格式化结果: {formatted[:100]}...")
        
        print(f"   📊 格式化成功率: {format_success}/{len(test_cases)}")
        
        # 测试挑战杯要求的JSON格式
        mock_result = {
            "result": {
                "results": [
                    {"id": "test_001", "content": "<think>测试思考</think><answer>Answer: A</answer>"}
                ],
                "duration": 123.45
            }
        }
        
        # 验证JSON结构
        required_keys = ["result"]
        result_keys = ["results", "duration"]
        
        json_valid = True
        for key in required_keys:
            if key not in mock_result:
                json_valid = False
                print(f"   ❌ JSON缺少键: {key}")
        
        for key in result_keys:
            if key not in mock_result["result"]:
                json_valid = False
                print(f"   ❌ result缺少键: {key}")
        
        if json_valid:
            print("   ✓ JSON格式验证通过")
        
        return format_success == len(test_cases) and json_valid
        
    except Exception as e:
        print(f"   ❌ 输出格式测试失败: {e}")
        return False

def test_performance_monitoring():
    """测试性能监控"""
    print("\n📊 性能监控测试:")
    
    try:
        from competition_model_challenge_cup import op_registry
        
        # 检查性能报告结构
        perf_report = op_registry.get_performance_report()
        
        required_fields = ["registered_ops", "performance", "total_registered", "fusion_enabled"]
        for field in required_fields:
            if field in perf_report:
                print(f"   ✓ {field}: {perf_report[field]}")
            else:
                print(f"   ❌ 缺少字段: {field}")
                return False
        
        # 检查挑战杯特有字段
        challenge_fields = ["challenge_cup_compliance"]
        for field in challenge_fields:
            if field in perf_report:
                print(f"   ✓ 挑战杯字段 {field}: {perf_report[field]}")
            else:
                print(f"   ⚠️ 挑战杯字段 {field}: 未找到")
        
        print("   ✓ 性能监控系统正常")
        return True
        
    except Exception as e:
        print(f"   ❌ 性能监控测试失败: {e}")
        return False

def test_challenge_cup_compliance():
    """测试挑战杯规范符合性"""
    print("\n🎯 挑战杯规范符合性测试:")
    
    # 检查关键要求
    compliance_checks = [
        ("算子融合优化", "必须有算子融合工作，否则性能不得分"),
        ("输出格式", "必须包含<think></think><answer></answer>格式"), 
        ("参数量输出", "必须输出Total parameters: X.XXB"),
        ("duration统计", "返回JSON必须包含duration(ms)"),
        ("相对路径", "模型路径必须使用相对引用"),
        ("离线环境", "所有依赖必须预先打包")
    ]
    
    compliance_score = 0
    
    # 检查competition_model_challenge_cup.py内容
    if os.path.exists("competition_model_challenge_cup.py"):
        with open("competition_model_challenge_cup.py", 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查关键代码片段
        checks = [
            ("enable_operator_fusion", "算子融合优化"),
            ("Total parameters:", "参数量输出"),
            ("<think></think><answer></answer>", "输出格式"),
            ("duration", "duration统计"),
            ("./", "相对路径"),
            ("challenge_cup", "挑战杯适配")
        ]
        
        for pattern, description in checks:
            if pattern in content:
                print(f"   ✓ {description} - 代码检查通过")
                compliance_score += 1
            else:
                print(f"   ⚠️ {description} - 代码检查可能缺失")
    
    # 检查构建脚本
    if os.path.exists("build_challenge_cup_submission.sh"):
        print("   ✓ 挑战杯构建脚本存在")
        compliance_score += 1
    else:
        print("   ❌ 挑战杯构建脚本缺失")
    
    # 检查指南文档
    if os.path.exists("挑战杯B榜自定义算子集成指南.md"):
        print("   ✓ 挑战杯集成指南存在") 
        compliance_score += 1
    else:
        print("   ❌ 挑战杯集成指南缺失")
    
    compliance_rate = compliance_score / (len(checks) + 2) * 100
    print(f"   📊 挑战杯规范符合率: {compliance_rate:.1f}%")
    
    return compliance_rate >= 80  # 80%符合率算通过

def run_all_tests():
    """运行所有测试"""
    print("🧪 开始完整集成测试...\n")
    
    tests = [
        ("环境设置", test_environment_setup),
        ("文件结构", test_file_structure), 
        ("算子注册", test_operator_registry),
        ("算子融合", test_operator_fusion),
        ("Competition类", test_competition_class),
        ("输出格式", test_output_format),
        ("性能监控", test_performance_monitoring),
        ("挑战杯规范", test_challenge_cup_compliance)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results[test_name] = result
            status = "✅ 通过" if result else "❌ 失败"
            print(f"\n{test_name}: {status}")
        except Exception as e:
            results[test_name] = False
            print(f"\n{test_name}: ❌ 异常 - {e}")
    
    # 汇总报告
    print("\n" + "=" * 60)
    print("🎯 挑战杯B榜集成测试报告")
    print("=" * 60)
    
    passed = sum(results.values())
    total = len(results)
    
    print(f"📊 总体成功率: {passed}/{total} ({passed/total*100:.1f}%)")
    print("\n📋 详细结果:")
    
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
    
    # 挑战杯关键检查
    critical_tests = ["算子融合", "输出格式", "挑战杯规范"]
    critical_passed = sum(results.get(test, False) for test in critical_tests)
    
    print(f"\n🏆 挑战杯关键功能: {critical_passed}/{len(critical_tests)}")
    
    if critical_passed == len(critical_tests):
        print("✅ 所有挑战杯关键功能测试通过！")
        print("🎯 系统已准备好进行挑战杯B榜提交")
    else:
        print("⚠️ 部分挑战杯关键功能需要修复")
        print("📝 请查看上述失败项目并进行修复")
    
    print("\n📋 下一步建议:")
    if passed >= total * 0.8:
        print("1. ✅ 运行 build_challenge_cup_submission.sh 构建提交包")
        print("2. ✅ 在ModelArts环境中进行完整测试")
        print("3. ✅ 验证算子融合性能提升")
        print("4. ✅ 准备挑战杯平台提交")
    else:
        print("1. 🔧 修复失败的测试项目")
        print("2. 🔧 重新运行集成测试")
        print("3. 🔧 确保所有关键功能正常")
    
    return passed >= total * 0.8

if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1) 