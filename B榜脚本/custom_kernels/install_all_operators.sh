#!/bin/bash

# 华为挑战杯B榜 - 标准化安装所有算子的脚本

echo "📦 开始安装所有算子..."

echo "📋 步骤1: 配置环境变量..."
if source ./环境配置脚本.sh; then
    echo "✅ 环境配置成功"
else
    echo "❌ 环境配置失败"
    exit 1
fi

# 实际的算子列表（根据当前目录）
OPERATORS=(
    "FlashAttentionScore"
    "GELU"
    "GlobalAvgPool"
    "HighPerfMatMulCustom"
    "LayerNormCustom"
    "MoeSoftMaxTopkCustom"
)

# 检查可用的算子包
echo "📋 检查可用算子包..."
AVAILABLE_PACKAGES=()
for op in "${OPERATORS[@]}"; do
    if [ -d "$op/build_out" ]; then
        run_files=$(ls "$op/build_out/"*.run 2>/dev/null)
        if [ -n "$run_files" ]; then
            AVAILABLE_PACKAGES+=("$op")
            echo "   ✅ 发现算子包: $op"
            for run_file in $run_files; do
                echo "      📦 $(basename $run_file)"
            done
        else
            echo "   ⚠️  $op 构建目录存在但无.run文件"
        fi
    else
        echo "   ⚠️  $op 构建目录不存在"
    fi
done

if [ ${#AVAILABLE_PACKAGES[@]} -eq 0 ]; then
    echo "❌ 没有找到可安装的算子包，请先运行构建脚本"
    exit 1
fi

echo "📋 总共找到 ${#AVAILABLE_PACKAGES[@]} 个算子包，开始安装..."

install_count=0
failed_count=0
CURRENT_DIR=$(pwd)

for op in "${AVAILABLE_PACKAGES[@]}"; do
    echo ""
    echo "📦 安装算子 [$((install_count + failed_count + 1))/${#AVAILABLE_PACKAGES[@]}]: $op"
    
    if cd "$op/build_out" 2>/dev/null; then
        echo "   📁 进入目录: $(pwd)"
        
        # 查找算子包
        run_files=$(ls *.run 2>/dev/null)
        if [ -n "$run_files" ]; then
            # 取第一个.run文件进行安装
            run_file=$(echo $run_files | awk '{print $1}')
            echo "   📦 找到算子包: $run_file"
            echo "   🔧 开始安装..."
            
            if bash "$run_file"; then
                echo "   ✅ $op 安装成功"
                ((install_count++))
            else
                echo "   ❌ $op 安装失败"
                ((failed_count++))
            fi
        else
            echo "   ❌ 未找到算子包文件"
            ((failed_count++))
        fi
        
        echo "   📁 返回目录..."
        cd "$CURRENT_DIR" || {
            echo "   ❌ 无法返回目录: $CURRENT_DIR"
            cd /home/<USER>/work/CANN-Op-Tuner/B榜脚本/custom_kernels
        }
    else
        echo "   ❌ 无法进入构建目录: $op/build_out"
        ((failed_count++))
    fi
    
    echo "   ⏱️  当前进度: 成功[$install_count] 失败[$failed_count]"
done

echo ""
echo "📊 安装完成统计："
echo "   ✅ 成功安装: $install_count 个算子"
echo "   ❌ 安装失败: $failed_count 个算子"
echo "   📁 总计: ${#AVAILABLE_PACKAGES[@]} 个算子"

# 验证安装
echo ""
echo "🔍 验证算子库安装..."
opp_path="$ASCEND_INSTALL_PATH/opp/vendors/customize"
if [ -d "$opp_path" ]; then
    echo "   📂 OPP路径: $opp_path"
    
    # 检查各个组件
    if [ -d "$opp_path/op_api/lib" ]; then
        api_count=$(ls "$opp_path/op_api/lib/"*.so 2>/dev/null | wc -l)
        echo "   🔗 API库: $api_count 个"
    fi
    
    if [ -d "$opp_path/op_impl" ]; then
        impl_count=$(find "$opp_path/op_impl" -name "*.py" 2>/dev/null | wc -l)
        echo "   🐍 实现文件: $impl_count 个"
    fi
    
    if [ -d "$opp_path/framework" ]; then
        framework_count=$(find "$opp_path/framework" -name "*.so" 2>/dev/null | wc -l)
        echo "   🔧 框架库: $framework_count 个"
    fi
    
    echo "   ✅ OPP算子库验证完成"
else
    echo "   ❌ OPP算子库路径不存在: $opp_path"
fi

# 最终结果
if [ $install_count -gt 0 ]; then
    echo ""
    echo "🎉 成功安装 $install_count 个算子！"
    
    if [ $failed_count -eq 0 ]; then
        echo "🏆 所有算子安装完成！"
        echo "💡 提示: 可以运行 python3 test_all_operators.py 测试算子"
        exit 0
    else
        echo "⚠️  部分算子安装失败，但有成功的算子可以使用"
        exit 0
    fi
else
    echo ""
    echo "💥 所有算子安装都失败了，请检查错误日志"
    exit 1
fi
