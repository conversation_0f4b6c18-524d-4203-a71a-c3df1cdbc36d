#!/bin/bash

# 华为挑战杯B榜 - 标准化构建所有算子的脚本

# 移除 set -e，避免任何非零退出码导致脚本终止
# set -e

echo "🚀 开始构建所有算子（标准化模式）..."

echo "📋 步骤1: 配置环境变量..."
if source ./环境配置脚本.sh; then
    echo "✅ 环境配置成功"
else
    echo "❌ 环境配置失败"
    exit 1
fi

echo "📋 步骤2: 开始构建各算子..."

# 算子列表（根据实际目录调整）
OPERATORS=(
    "FlashAttentionScore"
    "GELU"
    "GlobalAvgPool"
    "HighPerfMatMulCustom"
    "LayerNormCustom"
    "MoeSoftMaxTopkCustom"
)

# 检查实际存在的算子目录
echo "📋 检查可用算子目录..."
AVAILABLE_OPERATORS=()
for op in "${OPERATORS[@]}"; do
    if [ -d "$op" ]; then
        AVAILABLE_OPERATORS+=("$op")
        echo "   ✅ 发现算子: $op"
    else
        echo "   ⚠️  算子目录不存在: $op"
    fi
done

if [ ${#AVAILABLE_OPERATORS[@]} -eq 0 ]; then
    echo "❌ 没有找到可构建的算子目录"
    exit 1
fi

echo "📋 总共找到 ${#AVAILABLE_OPERATORS[@]} 个算子，开始构建..."

# 构建每个算子
build_count=0
failed_count=0
CURRENT_DIR=$(pwd)

for op in "${AVAILABLE_OPERATORS[@]}"; do
    echo ""
    echo "📦 构建算子 [$((build_count + failed_count + 1))/${#AVAILABLE_OPERATORS[@]}]: $op"
    echo "   📁 当前目录: $(pwd)"
    echo "   📁 进入目录: $op"
    
    if cd "$op" 2>/dev/null; then
        echo "   ✅ 成功进入目录: $(pwd)"
        
        # 清理之前的构建
        echo "   🧹 清理旧的构建文件..."
        rm -rf build_out/ 2>/dev/null || true
        
        # 执行标准化构建
        echo "   🔨 执行构建..."
        if bash build.sh; then
            echo "   ✅ $op 构建成功"
            ((build_count++))
            
            # 检查生成的.run文件
            echo "   🔍 检查生成的安装包..."
            if ls build_out/*.run >/dev/null 2>&1; then
                run_files=$(ls build_out/*.run 2>/dev/null)
                for run_file in $run_files; do
                    echo "   📦 生成安装包: $(basename $run_file)"
                done
            else
                echo "   ⚠️  未找到 .run 安装包文件"
            fi
        else
            echo "   ❌ $op 构建失败"
            ((failed_count++))
        fi
        
        echo "   📁 返回上级目录..."
        cd "$CURRENT_DIR" || {
            echo "   ❌ 无法返回目录: $CURRENT_DIR"
            cd /home/<USER>/work/CANN-Op-Tuner/B榜脚本/custom_kernels
        }
        echo "   📁 当前目录: $(pwd)"
    else
        echo "   ❌ 无法进入目录: $op"
        ((failed_count++))
    fi
    
    echo "   ⏱️  当前进度: 成功[$build_count] 失败[$failed_count]"
done

echo ""
echo "📊 构建完成统计："
echo "   ✅ 成功构建: $build_count 个算子"
echo "   ❌ 构建失败: $failed_count 个算子"
echo "   📁 总计: ${#AVAILABLE_OPERATORS[@]} 个算子"

if [ $build_count -gt 0 ]; then
    echo "🎉 有 $build_count 个算子构建成功！"
    
    # 显示生成的安装包
    echo "📦 生成的安装包："
    find . -name "*.run" -type f 2>/dev/null | while read run_file; do
        echo "   - $run_file"
    done
    
    if [ $failed_count -eq 0 ]; then
        echo "🏆 所有算子构建完成！"
        exit 0
    else
        echo "⚠️  部分算子构建失败，但有成功的算子可以使用"
        exit 0
    fi
else
    echo "💥 所有算子构建都失败了，请检查错误日志"
    exit 1
fi
