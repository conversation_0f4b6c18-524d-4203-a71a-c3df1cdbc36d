{"binFileName": "MatmulCustom_5e423f75aa7141507d7f564a13c9e383", "binFileSuffix": ".o", "blockDim": -1, "coreType": "AiCore", "core_type": "AIC", "intercoreSync": 0, "kernelName": "MatmulCustom_5e423f75aa7141507d7f564a13c9e383", "magic": "RT_DEV_BINARY_MAGIC_ELF_AICUBE", "memoryStamping": [], "opParaSize": 224, "parameters": [], "sha256": "44e7997fc4cc2eb1bdff28f5b7878cb1713db5604267768c94e5b774f240a297", "workspace": {"num": 1, "size": [-1], "type": [0]}, "kernelList": [{"kernelName": "MatmulCustom_5e423f75aa7141507d7f564a13c9e383_0"}], "optionalInputMode": "gen_placeholder", "optionalOutputMode": "gen_placeholder", "compileInfo": {}, "supportInfo": {"implMode": "", "int64Mode": false, "simplifiedKeyMode": 0, "simplifiedKey": ["MatmulCustom/d=0,p=0/0,2/0,2/0,2/0,2", "MatmulCustom/d=1,p=0/0,2/0,2/0,2/0,2"], "optionalInputMode": "gen_placeholder", "optionalOutputMode": "gen_placeholder", "staticKey": "10ece6b820443a55859e93e431b9869965527700534b902d054c8d6c436c9db4", "inputs": [{"name": "a", "index": 0, "dtype": "float32", "format": "ND", "paramType": "required", "shape": [-2]}, {"name": "b", "index": 1, "dtype": "float32", "format": "ND", "paramType": "required", "shape": [-2]}, {"name": "bias", "index": 2, "dtype": "float32", "format": "ND", "paramType": "required", "shape": [-2]}], "outputs": [{"name": "c", "index": 0, "dtype": "float32", "format": "ND", "paramType": "required", "shape": [-2]}], "opMode": "dynamic", "deterministic": "ignore"}}