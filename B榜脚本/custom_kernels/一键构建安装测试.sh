#!/bin/bash

# 华为挑战杯B榜 - 一键构建、安装、测试脚本
# 增强错误处理，确保脚本鲁棒性

# 移除set -e，使用更灵活的错误处理
echo "🎯 华为挑战杯B榜 - 一键部署测试"
echo "========================================"

# 记录开始时间
start_time=$(date +%s)

# 错误计数器
error_count=0
warning_count=0

# 步骤1：环境配置
echo ""
echo "📋 步骤1: 配置环境..."
echo "----------------------------------------"

if [ -f "./环境配置脚本.sh" ]; then
    # 使用 source 执行，这样 return 语句会正常工作
    if source ./环境配置脚本.sh; then
        echo "✅ 环境配置成功完成"
    else
        echo "⚠️  环境配置有警告，但继续执行"
        warning_count=$((warning_count + 1))
    fi
else
    echo "❌ 错误：环境配置脚本不存在"
    error_count=$((error_count + 1))
    echo "ℹ️  请确保 环境配置脚本.sh 文件存在"
fi

# 步骤2：构建所有算子
echo ""
echo "📋 步骤2: 构建所有算子..."
echo "----------------------------------------"

if [ -f "build_all_operators.sh" ]; then
    echo "🔨 开始构建算子..."
    if bash build_all_operators.sh; then
        echo "✅ 算子构建成功完成"
    else
        build_exit_code=$?
        echo "❌ 算子构建失败 (退出码: $build_exit_code)"
        error_count=$((error_count + 1))
        
        # 检查是否有部分成功的构建
        if ls */build_out/*.run 2>/dev/null | head -1 >/dev/null; then
            echo "ℹ️  检测到部分算子构建成功，继续执行"
            warning_count=$((warning_count + 1))
        else
            echo "⚠️  未发现任何成功构建的算子，但继续尝试安装步骤"
        fi
    fi
else
    echo "❌ 错误：构建脚本不存在"
    error_count=$((error_count + 1))
    echo "ℹ️  请确保 build_all_operators.sh 文件存在"
fi

# 检查构建结果
built_operators=0
if ls */build_out/*.run 2>/dev/null >/dev/null; then
    built_operators=$(ls */build_out/*.run 2>/dev/null | wc -l)
    echo "📊 构建统计: 发现 $built_operators 个.run文件"
    ls */build_out/*.run 2>/dev/null | head -5 | sed 's/^/   - /'
    if [ $built_operators -gt 5 ]; then
        echo "   ... 还有 $((built_operators - 5)) 个文件"
    fi
else
    echo "⚠️  未发现.run文件，可能构建失败"
fi

# 步骤3：安装所有算子
echo ""
echo "📋 步骤3: 安装所有算子..."
echo "----------------------------------------"

if [ -f "install_all_operators.sh" ]; then
    echo "📦 开始安装算子..."
    if bash install_all_operators.sh; then
        echo "✅ 算子安装成功完成"
    else
        install_exit_code=$?
        echo "❌ 算子安装失败 (退出码: $install_exit_code)"
        error_count=$((error_count + 1))
        
        # 检查是否有部分成功的安装
        opp_path="/home/<USER>/Ascend/ascend-toolkit/latest/opp/vendors/customize"
        if [ -d "$opp_path/op_api" ] && [ "$(ls -A $opp_path/op_api 2>/dev/null)" ]; then
            echo "ℹ️  检测到部分算子安装成功"
            warning_count=$((warning_count + 1))
        else
            echo "⚠️  未检测到成功安装的算子"
        fi
    fi
else
    echo "❌ 错误：安装脚本不存在"
    error_count=$((error_count + 1))
    echo "ℹ️  请确保 install_all_operators.sh 文件存在"
fi

# 检查安装结果
echo "📊 安装验证:"
opp_paths=(
    "/home/<USER>/Ascend/ascend-toolkit/latest/opp/vendors/customize"
    "/usr/local/Ascend/opp/vendors/customize"
)

installed_found=false
for opp_path in "${opp_paths[@]}"; do
    if [ -d "$opp_path" ]; then
        echo "   🔍 检查路径: $opp_path"
        
        # 检查API库
        if [ -d "$opp_path/op_api/lib" ]; then
            api_count=$(ls $opp_path/op_api/lib/*.so 2>/dev/null | wc -l)
            if [ $api_count -gt 0 ]; then
                echo "   ✅ API库: $api_count 个.so文件"
                installed_found=true
            fi
        fi
        
        # 检查实现库
        if [ -d "$opp_path/op_impl" ]; then
            impl_count=$(ls $opp_path/op_impl/*.so 2>/dev/null | wc -l)
            if [ $impl_count -gt 0 ]; then
                echo "   ✅ 实现库: $impl_count 个.so文件"
                installed_found=true
            fi
        fi
        
        # 检查头文件
        if [ -d "$opp_path/op_api/include" ]; then
            header_count=$(ls $opp_path/op_api/include/aclnn_*.h 2>/dev/null | wc -l)
            if [ $header_count -gt 0 ]; then
                echo "   ✅ 头文件: $header_count 个.h文件"
                installed_found=true
            fi
        fi
        break
    fi
done

if [ "$installed_found" = false ]; then
    echo "   ⚠️  未发现已安装的算子库"
    warning_count=$((warning_count + 1))
fi

# # 步骤4：可选测试（当前注释掉）
# echo ""
# echo "📋 步骤4: 运行测试..."
# echo "----------------------------------------"
# echo "ℹ️  测试步骤当前已注释，跳过执行"
# echo "ℹ️  如需测试，请手动运行相关测试脚本"

# # 总结报告
# echo ""
# echo "========================================"
# end_time=$(date +%s)
# duration=$((end_time - start_time))

# echo "🎯 部署完成报告"
# echo "========================================"
# echo "⏱️  总耗时: ${duration}秒"
# echo "📊 执行统计:"
# echo "   - 错误数: $error_count"
# echo "   - 警告数: $warning_count"

# if [ $error_count -eq 0 ]; then
#     echo "🎉 ✅ 部署完全成功！算子已准备就绪。"
#     exit_status=0
# elif [ $error_count -le 2 ] && [ "$installed_found" = true ]; then
#     echo "🎉 ⚠️  部署基本成功！有少量警告但算子可用。"
#     exit_status=0
# else
#     echo "❌ 部署过程中遇到较多问题，请检查上述错误信息。"
#     exit_status=1
# fi

# echo ""
# echo "📋 下一步建议:"
# if [ $exit_status -eq 0 ]; then
#     echo "1. ✅ 算子已准备就绪，可以开始使用"
#     echo "2. ✅ 可以运行 test_challenge_cup_integration.py 进行完整测试"
#     echo "3. ✅ 可以使用 build_challenge_cup_submission.sh 构建提交包"
# else
#     echo "1. 🔧 请检查并修复上述错误"
#     echo "2. 🔧 确保CANN环境正确安装和配置"
#     echo "3. 🔧 检查算子源码和构建配置"
#     echo "4. 🔧 重新运行此脚本"
# fi

echo "========================================"

# 返回适当的退出状态
exit $exit_status
