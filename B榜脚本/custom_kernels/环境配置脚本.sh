#!/bin/bash

# 华为挑战杯B榜 - 标准化环境配置脚本
# 基于成功的AddCustom样例进行标准化配置

echo "🚀 配置AscendC算子开发环境..."

# 使用标准的CANN环境变量（与成功样例一致）
export ASCEND_INSTALL_PATH=${ASCEND_INSTALL_PATH:-/home/<USER>/Ascend/ascend-toolkit/latest}

# 检查CANN环境（不再直接exit，改为return）
if [ ! -d "$ASCEND_INSTALL_PATH" ]; then
    echo "❌ 错误：CANN环境未找到，请确认路径：$ASCEND_INSTALL_PATH"
    echo "ℹ️  请尝试检查以下路径："
    echo "   - /home/<USER>/Ascend/ascend-toolkit/latest"
    echo "   - /usr/local/Ascend/ascend-toolkit/latest"
    # 不再直接exit，让调用者决定如何处理
    return 1 2>/dev/null || exit 1
fi

# 设置标准环境变量（与AddCustom样例一致）
echo "📦 加载CANN环境变量..."

# 更安全的setenv.bash加载方式
if [ -f "$ASCEND_INSTALL_PATH/bin/setenv.bash" ]; then
    # 使用 || true 确保即使有警告也不会导致脚本退出
    source $ASCEND_INSTALL_PATH/bin/setenv.bash >/dev/null 2>&1 || true
    echo "   ✅ setenv.bash 加载完成"
else
    echo "   ⚠️  setenv.bash 未找到，手动设置环境变量"
fi

# 设置必要的环境变量
export DDK_PATH=$ASCEND_INSTALL_PATH
export NPU_HOST_LIB=$ASCEND_INSTALL_PATH/$(arch)-$(uname -s | tr '[:upper:]' '[:lower:]')/devlib

echo "✅ 环境配置完成："
echo "   ASCEND_INSTALL_PATH: $ASCEND_INSTALL_PATH"
echo "   DDK_PATH: $DDK_PATH"
echo "   NPU_HOST_LIB: $NPU_HOST_LIB"

# 检查NPU设备（简化输出，增强错误处理）
echo "📊 检查NPU设备状态..."
if command -v npu-smi &> /dev/null; then
    # 使用 || true 确保npu-smi的任何输出都不会导致脚本失败
    npu_status=$(npu-smi info 2>/dev/null | grep -E "910.*OK" | head -1 || true)
    if [ -n "$npu_status" ]; then
        echo "   ✅ NPU设备正常: $(echo $npu_status | awk '{print $2, $4}' 2>/dev/null || echo '检测到NPU设备')"
    else
        echo "   ⚠️  NPU设备状态未知，但继续执行"
    fi
else
    echo "   ⚠️  npu-smi命令未找到，请检查CANN环境（但继续执行）"
fi

# 验证关键工具是否可用
echo "🔧 验证编译工具..."
tools_status=0

if command -v ascendc &> /dev/null; then
    echo "   ✅ ascendc 编译器可用"
else
    echo "   ⚠️  ascendc 编译器未找到"
    tools_status=1
fi

if [ -d "$ASCEND_INSTALL_PATH/compiler" ]; then
    echo "   ✅ CANN 编译器目录存在"
else
    echo "   ⚠️  CANN 编译器目录未找到"
    tools_status=1
fi

if [ $tools_status -eq 0 ]; then
    echo "🎉 环境配置完成，所有工具可用，可以开始构建算子！"
else
    echo "⚠️  环境配置完成，但部分工具可能不可用，尝试继续执行..."
fi

# 返回0确保不会导致调用脚本失败
return 0 2>/dev/null || exit 0 